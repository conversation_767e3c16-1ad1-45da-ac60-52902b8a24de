<?php
/**
 * صفحة إضافة عميل جديد - محدثة لاستخدام النظام الجديد OOP
 */

// تحميل النظام
require_once 'config/autoload.php';

// إعداد متغيرات الصفحة
$pageTitle = 'إضافة عميل جديد';
$currentPage = 'add_customer';
$showBreadcrumb = true;
$breadcrumbs = [
    ['title' => 'إضافة عميل جديد']
];

$message = '';
$customerManager = new Customer();

// معالجة إضافة عميل جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    $customerData = [
        'name' => $_POST['name'] ?? '',
        'phone' => $_POST['phone'] ?? '',
        'email' => $_POST['email'] ?? '',
        'address' => $_POST['address'] ?? ''
    ];

    $result = $customerManager->create($customerData);

    if ($result['success']) {
        $customer_id = $result['customer_id'];
        $message = '<div class="alert alert-success alert-custom">
            <i class="fas fa-check-circle"></i> ' . $result['message'] . '
            <div class="mt-2">
                <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                </a>
                <a href="add_customer.php" class="btn btn-sm btn-primary">
                    <i class="fas fa-user-plus"></i> إضافة عميل آخر
                </a>
            </div>
        </div>';
    } else {
        $message = '<div class="alert alert-danger alert-custom">
            <i class="fas fa-exclamation-triangle"></i> ' . $result['message'] . '
        </div>';
    }
}

// تضمين الـ header
include INCLUDES_PATH . '/header.php';
?>

<div class="container mt-4">
        <?php echo $message; ?>
        
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-user-plus"></i> إضافة عميل جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="أدخل اسم العميل الكامل">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i> رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="مثال: 01234567890">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="مثال: <EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="أدخل العنوان التفصيلي"></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ العميل
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح مفيدة -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-lightbulb text-warning"></i> نصائح مفيدة</h6>
                        <ul class="mb-0">
                            <li>اسم العميل مطلوب، باقي البيانات اختيارية</li>
                            <li>يمكنك إضافة طلبية مباشرة بعد حفظ العميل</li>
                            <li>تأكد من صحة رقم الهاتف للتواصل</li>
                            <li>العنوان مهم لتسليم الطلبيات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// JavaScript مخصص للصفحة
$inlineJS = "
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز على حقل الاسم
        document.getElementById('name').focus();

        // مراقبة التغييرات في النموذج
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input, textarea');

        inputs.forEach(input => {
            input.addEventListener('input', markAsChanged);
        });

        // تحسين إرسال النموذج
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type=\"submit\"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class=\"loading-spinner\"></span> جاري الحفظ...';
            markAsSaved();
        });
    });
";

// تضمين الـ footer
include INCLUDES_PATH . '/footer.php';
?>
