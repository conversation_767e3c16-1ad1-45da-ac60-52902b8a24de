<?php
/**
 * كلاس الطلبيات لنظام WIDDX OMS
 * يحتوي على جميع العمليات المتعلقة بإدارة الطلبيات
 */

require_once 'Database.php';

class Order {
    private $db;
    private $id;
    private $customer_id;
    private $status;
    private $notes;
    private $total_amount;
    private $created_at;
    private $updated_at;
    
    // حالات الطلبيات المسموحة
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    // إضافة طلبية جديدة
    public function create($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateData($data);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message']
                ];
            }
            
            $this->db->beginTransaction();
            
            // إنشاء الطلبية
            $query = "INSERT INTO orders (customer_id, notes, status, total_amount) 
                     VALUES (:customer_id, :notes, :status, :total_amount)";
            
            $this->db->query($query);
            $this->db->bind(':customer_id', $data['customer_id']);
            $this->db->bind(':notes', trim($data['notes'] ?? ''));
            $this->db->bind(':status', self::STATUS_PENDING);
            $this->db->bind(':total_amount', $data['total_amount'] ?? 0);
            
            if ($this->db->execute()) {
                $orderId = $this->db->lastInsertId();
                
                // إضافة عناصر الطلبية إذا كانت موجودة
                if (!empty($data['items'])) {
                    $itemsResult = $this->addOrderItems($orderId, $data['items']);
                    if (!$itemsResult['success']) {
                        $this->db->rollBack();
                        return $itemsResult;
                    }
                }
                
                $this->db->commit();
                
                return [
                    'success' => true,
                    'message' => 'تم إضافة الطلبية بنجاح',
                    'order_id' => $orderId
                ];
            } else {
                $this->db->rollBack();
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في إضافة الطلبية'
                ];
            }
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // تحديث حالة الطلبية
    public function updateStatus($id, $status) {
        try {
            // التحقق من صحة الحالة
            if (!$this->isValidStatus($status)) {
                return [
                    'success' => false,
                    'message' => 'حالة الطلبية غير صحيحة'
                ];
            }
            
            // التحقق من وجود الطلبية
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'الطلبية غير موجودة'
                ];
            }
            
            // تحديث الحالة
            $query = "UPDATE orders 
                     SET status = :status, updated_at = CURRENT_TIMESTAMP 
                     WHERE id = :id";
            
            $this->db->query($query);
            $this->db->bind(':status', $status);
            $this->db->bind(':id', $id);
            
            if ($this->db->execute()) {
                // الحصول على معلومات العميل للرسالة
                $orderInfo = $this->getOrderWithCustomer($id);
                $statusText = $this->getStatusText($status);
                
                return [
                    'success' => true,
                    'message' => "تم تحديث طلبية #{$id} للعميل {$orderInfo['customer_name']} إلى: {$statusText}"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في تحديث الطلبية'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // حذف طلبية
    public function delete($id) {
        try {
            // التحقق من وجود الطلبية
            if (!$this->exists($id)) {
                return [
                    'success' => false,
                    'message' => 'الطلبية غير موجودة'
                ];
            }
            
            $this->db->beginTransaction();
            
            // حذف عناصر الطلبية أولاً
            $query = "DELETE FROM order_items WHERE order_id = :order_id";
            $this->db->query($query);
            $this->db->bind(':order_id', $id);
            $this->db->execute();
            
            // حذف الطلبية
            $query = "DELETE FROM orders WHERE id = :id";
            $this->db->query($query);
            $this->db->bind(':id', $id);
            
            if ($this->db->execute()) {
                $this->db->commit();
                return [
                    'success' => true,
                    'message' => 'تم حذف الطلبية بنجاح'
                ];
            } else {
                $this->db->rollBack();
                return [
                    'success' => false,
                    'message' => 'حدث خطأ في حذف الطلبية'
                ];
            }
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'message' => 'خطأ في النظام: ' . $e->getMessage()
            ];
        }
    }
    
    // الحصول على طلبية مع معلومات العميل
    public function getOrderWithCustomer($id) {
        try {
            $query = "SELECT o.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
                     FROM orders o 
                     JOIN customers c ON o.customer_id = c.id 
                     WHERE o.id = :id";
            
            $this->db->query($query);
            $this->db->bind(':id', $id);
            
            return $this->db->single();
        } catch (Exception $e) {
            return null;
        }
    }
    
    // الحصول على جميع الطلبيات مع الفلترة
    public function getAllWithFilters($filters = []) {
        try {
            $whereConditions = [];
            $params = [];
            
            // فلتر العميل
            if (!empty($filters['customer_id'])) {
                $whereConditions[] = "o.customer_id = :customer_id";
                $params[':customer_id'] = $filters['customer_id'];
            }
            
            // فلتر الحالة
            if (!empty($filters['status'])) {
                $whereConditions[] = "o.status = :status";
                $params[':status'] = $filters['status'];
            }
            
            // فلتر التاريخ
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(o.created_at) >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(o.created_at) <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }
            
            $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
            
            $query = "SELECT o.*, c.name as customer_name, c.phone as customer_phone,
                     (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as items_count
                     FROM orders o 
                     JOIN customers c ON o.customer_id = c.id 
                     {$whereClause}
                     ORDER BY o.created_at DESC";
            
            $this->db->query($query);
            
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->resultset();
        } catch (Exception $e) {
            return [];
        }
    }
    
    // الحصول على طلبيات العميل
    public function getCustomerOrders($customerId) {
        try {
            $query = "SELECT o.*, 
                     (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as items_count
                     FROM orders o 
                     WHERE o.customer_id = :customer_id 
                     ORDER BY 
                     CASE 
                         WHEN o.status = 'pending' THEN 1
                         WHEN o.status = 'processing' THEN 2
                         WHEN o.status = 'completed' THEN 3
                         WHEN o.status = 'cancelled' THEN 4
                     END,
                     o.created_at DESC";
            
            $this->db->query($query);
            $this->db->bind(':customer_id', $customerId);
            
            return $this->db->resultset();
        } catch (Exception $e) {
            return [];
        }
    }
    
    // إضافة عناصر للطلبية
    private function addOrderItems($orderId, $items) {
        try {
            foreach ($items as $item) {
                $query = "INSERT INTO order_items 
                         (order_id, product_name, quantity, width, height, depth, unit_price, total_price, notes) 
                         VALUES (:order_id, :product_name, :quantity, :width, :height, :depth, :unit_price, :total_price, :notes)";
                
                $this->db->query($query);
                $this->db->bind(':order_id', $orderId);
                $this->db->bind(':product_name', $item['product_name']);
                $this->db->bind(':quantity', $item['quantity']);
                $this->db->bind(':width', $item['width'] ?? null);
                $this->db->bind(':height', $item['height'] ?? null);
                $this->db->bind(':depth', $item['depth'] ?? null);
                $this->db->bind(':unit_price', $item['unit_price'] ?? 0);
                $this->db->bind(':total_price', $item['total_price'] ?? 0);
                $this->db->bind(':notes', $item['notes'] ?? '');
                
                if (!$this->db->execute()) {
                    return [
                        'success' => false,
                        'message' => 'حدث خطأ في إضافة عناصر الطلبية'
                    ];
                }
            }
            
            return ['success' => true];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إضافة عناصر الطلبية: ' . $e->getMessage()
            ];
        }
    }
    
    // التحقق من صحة البيانات
    private function validateData($data) {
        $errors = [];
        
        // التحقق من معرف العميل
        if (empty($data['customer_id']) || !is_numeric($data['customer_id'])) {
            $errors[] = 'معرف العميل مطلوب';
        }
        
        // التحقق من المبلغ الإجمالي
        if (isset($data['total_amount']) && !is_numeric($data['total_amount'])) {
            $errors[] = 'المبلغ الإجمالي يجب أن يكون رقماً';
        }
        
        return [
            'valid' => empty($errors),
            'message' => implode(', ', $errors)
        ];
    }
    
    // التحقق من صحة الحالة
    private function isValidStatus($status) {
        $validStatuses = [
            self::STATUS_PENDING,
            self::STATUS_PROCESSING,
            self::STATUS_COMPLETED,
            self::STATUS_CANCELLED
        ];
        
        return in_array($status, $validStatuses);
    }
    
    // التحقق من وجود الطلبية
    private function exists($id) {
        $query = "SELECT id FROM orders WHERE id = :id";
        $this->db->query($query);
        $this->db->bind(':id', $id);
        $result = $this->db->single();
        
        return !empty($result);
    }
    
    // الحصول على نص الحالة بالعربية
    private function getStatusText($status) {
        $statusTexts = [
            self::STATUS_PENDING => 'معلقة',
            self::STATUS_PROCESSING => 'قيد التنفيذ',
            self::STATUS_COMPLETED => 'مكتملة',
            self::STATUS_CANCELLED => 'ملغية'
        ];
        
        return $statusTexts[$status] ?? 'غير محدد';
    }
    
    // الحصول على إحصائيات الطلبيات
    public function getStats() {
        try {
            $stats = [];
            
            // إجمالي الطلبيات
            $query = "SELECT COUNT(*) as total FROM orders";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['total_orders'] = $result['total'];
            
            // الطلبيات المعلقة
            $query = "SELECT COUNT(*) as pending FROM orders WHERE status = 'pending'";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['pending_orders'] = $result['pending'];
            
            // الطلبيات قيد التنفيذ
            $query = "SELECT COUNT(*) as processing FROM orders WHERE status = 'processing'";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['processing_orders'] = $result['processing'];
            
            // الطلبيات المكتملة
            $query = "SELECT COUNT(*) as completed FROM orders WHERE status = 'completed'";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['completed_orders'] = $result['completed'];
            
            // إجمالي المبيعات
            $query = "SELECT SUM(total_amount) as total_sales FROM orders WHERE status = 'completed'";
            $this->db->query($query);
            $result = $this->db->single();
            $stats['total_sales'] = $result['total_sales'] ?? 0;
            
            return $stats;
        } catch (Exception $e) {
            return [];
        }
    }
}
