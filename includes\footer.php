    </main>
    <!-- نهاية المحتوى الرئيسي -->

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4 no-print">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-box"></i> WIDDX OMS</h5>
                    <p class="mb-2">نظام إدارة الطلبيات الشامل</p>
                    <p class="text-muted small">
                        نظام متطور لإدارة العملاء والطلبيات مع واجهة سهلة الاستخدام
                    </p>
                </div>
                <div class="col-md-3">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-light text-decoration-none">
                            <i class="fas fa-home"></i> الرئيسية
                        </a></li>
                        <li><a href="add_order.php" class="text-light text-decoration-none">
                            <i class="fas fa-plus-circle"></i> إضافة طلبية
                        </a></li>
                        <li><a href="view_orders.php" class="text-light text-decoration-none">
                            <i class="fas fa-list"></i> عرض الطلبيات
                        </a></li>
                        <li><a href="manage_customers.php" class="text-light text-decoration-none">
                            <i class="fas fa-users-cog"></i> إدارة العملاء
                        </a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>معلومات النظام</h6>
                    <ul class="list-unstyled">
                        <li><small class="text-muted">
                            <i class="fas fa-code"></i> الإصدار: 2.0.0
                        </small></li>
                        <li><small class="text-muted">
                            <i class="fas fa-calendar"></i> آخر تحديث: <?php echo date('Y-m-d'); ?>
                        </small></li>
                        <li><small class="text-muted">
                            <i class="fas fa-server"></i> الخادم: <?php echo $_SERVER['SERVER_NAME'] ?? 'localhost'; ?>
                        </small></li>
                        <li><small class="text-muted">
                            <i class="fas fa-database"></i> قاعدة البيانات: MySQL
                        </small></li>
                    </ul>
                </div>
            </div>
            <hr class="my-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> WIDDX OMS. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        تم التطوير بواسطة <strong>WIDDX Team</strong>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- زر العودة لأعلى -->
    <button id="back-to-top" class="btn btn-primary position-fixed bottom-0 end-0 m-3 rounded-circle no-print" 
            style="display: none; z-index: 1000; width: 50px; height: 50px;" 
            onclick="scrollToTop()" title="العودة لأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <!-- Additional JavaScript if provided -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript for specific pages -->
    <?php if (isset($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>

    <!-- Global JavaScript functions -->
    <script>
        // إظهار/إخفاء زر العودة لأعلى
        window.addEventListener('scroll', function() {
            const backToTopButton = document.getElementById('back-to-top');
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        // وظيفة العودة لأعلى
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // إظهار مؤشر التحميل العام
        function showGlobalLoading() {
            document.getElementById('global-loading').style.display = 'block';
        }

        // إخفاء مؤشر التحميل العام
        function hideGlobalLoading() {
            document.getElementById('global-loading').style.display = 'none';
        }

        // وظيفة عرض رسالة عامة
        function showGlobalMessage(message, type = 'info', duration = 5000) {
            const container = document.getElementById('global-messages-container');
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';

            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-triangle',
                'warning': 'fas fa-exclamation-circle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-info-circle';

            const alertId = 'alert-' + Date.now();
            const alertHtml = `
                <div class="container mt-3">
                    <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="${icon}"></i> ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', alertHtml);

            // إزالة الرسالة تلقائياً
            if (duration > 0) {
                setTimeout(() => {
                    const alert = document.getElementById(alertId);
                    if (alert) {
                        alert.remove();
                    }
                }, duration);
            }
        }

        // تحسين تجربة المستخدم - تأكيد قبل مغادرة الصفحة إذا كان هناك تغييرات غير محفوظة
        let hasUnsavedChanges = false;

        function markAsChanged() {
            hasUnsavedChanges = true;
        }

        function markAsSaved() {
            hasUnsavedChanges = false;
        }

        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟';
                return e.returnValue;
            }
        });

        // مراقبة التغييرات في النماذج
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.addEventListener('change', markAsChanged);
                    input.addEventListener('input', markAsChanged);
                });

                // إزالة العلامة عند الإرسال
                form.addEventListener('submit', markAsSaved);
            });
        });

        // وظيفة طباعة محسنة
        function printPage() {
            window.print();
        }

        // وظيفة تصدير البيانات
        function exportData(format, type, filters = {}) {
            const params = new URLSearchParams({
                format: format,
                type: type,
                ...filters
            });
            
            const url = `export.php?${params.toString()}`;
            window.open(url, '_blank');
        }

        // وظيفة نسخ النص إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showGlobalMessage('تم نسخ النص إلى الحافظة', 'success', 2000);
            }).catch(function(err) {
                console.error('Error copying text: ', err);
                showGlobalMessage('فشل في نسخ النص', 'error', 2000);
            });
        }

        // تحسين الأداء - تحميل الصور بشكل تدريجي
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        });

        // تحسين إمكانية الوصول - دعم لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl+S للحفظ
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const saveButton = document.querySelector('button[type="submit"], .btn-save');
                if (saveButton) {
                    saveButton.click();
                }
            }
            
            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                });
            }
        });

        // إحصائيات الاستخدام (اختيارية)
        function trackPageView() {
            // يمكن إضافة كود تتبع الاستخدام هنا
            console.log('Page viewed:', window.location.pathname);
        }

        // تشغيل تتبع الصفحة
        document.addEventListener('DOMContentLoaded', trackPageView);
    </script>

    <!-- تحسينات الأداء -->
    <script>
        // تحسين الأداء - تأجيل تحميل المحتوى غير الضروري
        if ('requestIdleCallback' in window) {
            requestIdleCallback(function() {
                // تحميل المحتوى غير الضروري هنا
                console.log('Loading non-critical content...');
            });
        }
    </script>

</body>
</html>
