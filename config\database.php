<?php
/**
 * ملف قاعدة البيانات المحدث - يستخدم الآن الإعدادات الجديدة
 * للتوافق مع الملفات القديمة
 */

// تحميل الإعدادات الجديدة
require_once __DIR__ . '/config.php';

// تحميل كلاس قاعدة البيانات المحسن
require_once CLASSES_PATH . '/Database.php';

// إنشاء مثيل من قاعدة البيانات للتوافق مع الكود القديم
function getDatabase() {
    return Database::getInstance();
}

// كلاس قاعدة البيانات القديم للتوافق
class Database_Legacy {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $dbh;
    private $stmt;
    private $error;

    public function __construct() {
        // إعداد DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';

        // إعداد الخيارات
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        );

        // إنشاء اتصال PDO
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            Helper::logError("Database connection failed: " . $e->getMessage());
            if (DEBUG_MODE) {
                echo $this->error;
            }
        }
    }

    // تحضير الاستعلام
    public function query($query) {
        $this->stmt = $this->dbh->prepare($query);
    }

    // ربط القيم
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // تنفيذ الاستعلام
    public function execute() {
        return $this->stmt->execute();
    }

    // الحصول على النتائج
    public function resultset() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // الحصول على نتيجة واحدة
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }

    // عدد الصفوف
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // آخر ID مدرج
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }

    // بدء المعاملة
    public function beginTransaction() {
        return $this->dbh->beginTransaction();
    }

    // تأكيد المعاملة
    public function commit() {
        return $this->dbh->commit();
    }

    // إلغاء المعاملة
    public function rollBack() {
        return $this->dbh->rollBack();
    }

    // إغلاق الاتصال
    public function close() {
        $this->dbh = null;
    }
}

// استخدام الكلاس الجديد كافتراضي مع الحفاظ على التوافق
class Database extends Database_Legacy {
    private static $instance = null;

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // منع الاستنساخ
    private function __clone() {}

    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
?>
