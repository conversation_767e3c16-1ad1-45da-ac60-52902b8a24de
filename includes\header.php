<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'نظام إدارة الطلبيات'; ?> - WIDDX OMS</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/main.css" rel="stylesheet">
    
    <!-- Additional CSS if provided -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="نظام إدارة الطلبيات WIDDX OMS - نظام شامل لإدارة العملاء والطلبيات">
    <meta name="keywords" content="إدارة الطلبيات, نظام إدارة, عملاء, طلبيات, WIDDX">
    <meta name="author" content="WIDDX">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Custom styles for specific pages -->
    <style>
        <?php if (isset($customStyles)): ?>
            <?php echo $customStyles; ?>
        <?php endif; ?>
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'index' ? 'active' : ''; ?>" href="index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'add_customer' ? 'active' : ''; ?>" href="add_customer.php">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'add_order' ? 'active' : ''; ?>" href="add_order.php">
                            <i class="fas fa-plus-circle"></i> إضافة طلبية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'view_orders' ? 'active' : ''; ?>" href="view_orders.php">
                            <i class="fas fa-list"></i> عرض الطلبيات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage ?? '') == 'manage_customers' ? 'active' : ''; ?>" href="manage_customers.php">
                            <i class="fas fa-users-cog"></i> إدارة العملاء
                        </a>
                    </li>
                    
                    <!-- قائمة منسدلة للتقارير والأدوات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tools"></i> أدوات
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="system_check.php">
                                <i class="fas fa-check-circle"></i> فحص النظام
                            </a></li>
                            <li><a class="dropdown-item" href="update_database.php">
                                <i class="fas fa-database"></i> تحديث قاعدة البيانات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="widdxSystem.exportData('excel', 'orders')">
                                <i class="fas fa-file-excel"></i> تصدير الطلبيات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="widdxSystem.exportData('excel', 'customers')">
                                <i class="fas fa-file-excel"></i> تصدير العملاء
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- معلومات المستخدم والإشعارات -->
                <ul class="navbar-nav">
                    <!-- عداد الإشعارات -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" 
                                  id="notifications-count" style="display: none;">
                                0
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li id="notifications-list">
                                <a class="dropdown-item text-muted" href="#">
                                    <i class="fas fa-info-circle"></i> لا توجد إشعارات جديدة
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <!-- معلومات سريعة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="quickStatsDropdown" role="button" 
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-bar"></i> إحصائيات سريعة
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickStatsDropdown">
                            <li><h6 class="dropdown-header">إحصائيات اليوم</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-shopping-cart text-primary"></i> 
                                <span id="today-orders">0</span> طلبية جديدة
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-users text-success"></i> 
                                <span id="today-customers">0</span> عميل جديد
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-money-bill text-warning"></i> 
                                <span id="today-sales">0</span> جنيه مبيعات
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- حاوي الرسائل العامة -->
    <div id="global-messages-container"></div>

    <!-- مؤشر التحميل العام -->
    <div id="global-loading" style="display: none;">
        <div class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" 
             style="background-color: rgba(0,0,0,0.5); z-index: 9999;">
            <div class="text-center text-white">
                <div class="loading-spinner mb-3" style="width: 50px; height: 50px; border-width: 5px;"></div>
                <h5>جاري التحميل...</h5>
            </div>
        </div>
    </div>

    <!-- بداية المحتوى الرئيسي -->
    <main class="main-content">
        <?php if (isset($showBreadcrumb) && $showBreadcrumb): ?>
            <!-- مسار التنقل -->
            <div class="container mt-3">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                        <?php if (isset($breadcrumbs)): ?>
                            <?php foreach ($breadcrumbs as $breadcrumb): ?>
                                <?php if (isset($breadcrumb['url'])): ?>
                                    <li class="breadcrumb-item">
                                        <a href="<?php echo $breadcrumb['url']; ?>"><?php echo $breadcrumb['title']; ?></a>
                                    </li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo $breadcrumb['title']; ?>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ol>
                </nav>
            </div>
        <?php endif; ?>

    <!-- JavaScript للتحقق من الإشعارات -->
    <script>
        // تحديث الإشعارات والإحصائيات السريعة
        function updateQuickStats() {
            fetch('api/quick_stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('today-orders').textContent = data.stats.today_orders || 0;
                        document.getElementById('today-customers').textContent = data.stats.today_customers || 0;
                        document.getElementById('today-sales').textContent = data.stats.today_sales || 0;
                        
                        // تحديث عداد الإشعارات
                        const notificationsCount = data.notifications?.length || 0;
                        const badge = document.getElementById('notifications-count');
                        if (notificationsCount > 0) {
                            badge.textContent = notificationsCount;
                            badge.style.display = 'block';
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                })
                .catch(error => console.error('Error updating quick stats:', error));
        }

        // تحديث الإحصائيات كل دقيقة
        setInterval(updateQuickStats, 60000);
        
        // تحديث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateQuickStats);
    </script>
