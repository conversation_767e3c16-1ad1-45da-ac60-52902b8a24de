/* ملف CSS الرئيسي لنظام WIDDX OMS */

/* الإعدادات العامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

/* شريط التنقل */
.navbar-brand {
    font-weight: bold;
    color: #2c3e50 !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
    color: #212529;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: white;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white;
}

/* أيقونات الميزات */
.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

/* أزرار العملاء */
.customer-button {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px !important;
    transition: all 0.3s ease;
}

.customer-button:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.customer-button:not(.collapsed) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* إحصائيات العملاء */
.customer-stats .badge {
    font-size: 0.75rem;
}

/* الأكورديون */
.accordion-item {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.accordion-body {
    background-color: #f8f9fa;
}

/* صفوف الطلبيات */
.order-row {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.order-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* عناصر الطلبيات */
.order-item {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* شارات الحالة */
.status-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* صفوف العملاء */
.customer-row {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.customer-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.customer-active {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
}

.customer-inactive {
    border-left: 4px solid #6c757d;
    opacity: 0.8;
}

/* نماذج الإدخال */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تأثيرات الحركة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
}

/* تحسينات الشاشات الصغيرة */
@media (max-width: 768px) {
    .customer-stats {
        margin-top: 10px;
    }
    
    .customer-stats .badge {
        display: block;
        margin-bottom: 5px;
    }
    
    .order-row .row > div {
        margin-bottom: 10px;
    }
}

/* تحسينات إضافية */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات الألوان */
.text-primary-custom {
    color: #667eea !important;
}

.bg-primary-custom {
    background-color: #667eea !important;
}

.border-primary-custom {
    border-color: #667eea !important;
}

/* تحسينات التفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    opacity: 0.8;
}

/* تحسينات النصوص */
.text-truncate-custom {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

/* تحسينات الجداول */
.table-responsive-custom {
    border-radius: 10px;
    overflow: hidden;
}

.table-custom {
    margin-bottom: 0;
}

.table-custom th {
    background-color: #667eea;
    color: white;
    border: none;
    font-weight: 600;
}

.table-custom td {
    border-color: #e9ecef;
    vertical-align: middle;
}

/* تحسينات الرسائل */
.alert-custom {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأيقونات */
.icon-lg {
    font-size: 1.5rem;
}

.icon-xl {
    font-size: 2rem;
}

.icon-xxl {
    font-size: 3rem;
}
