<?php
/**
 * ملف اختبار النظام الجديد
 * يختبر جميع الكلاسات والوظائف الجديدة
 */

// تحميل النظام
require_once 'config/autoload.php';

echo "<h1>اختبار نظام WIDDX OMS الجديد</h1>";
echo "<hr>";

// اختبار قاعدة البيانات
echo "<h2>1. اختبار قاعدة البيانات</h2>";
try {
    $db = Database::getInstance();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    $info = $db->getDatabaseInfo();
    echo "📊 معلومات قاعدة البيانات:<br>";
    echo "- الخادم: " . $info['host'] . "<br>";
    echo "- قاعدة البيانات: " . $info['database'] . "<br>";
    echo "- إصدار MySQL: " . $info['mysql_version'] . "<br>";
    echo "- حجم قاعدة البيانات: " . $info['size_mb'] . " ميجابايت<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار كلاس العملاء
echo "<h2>2. اختبار كلاس العملاء</h2>";
try {
    $customerManager = new Customer();
    
    // اختبار جلب الإحصائيات
    $stats = $customerManager->getStats();
    echo "✅ إحصائيات العملاء:<br>";
    echo "- إجمالي العملاء: " . ($stats['total_customers'] ?? 0) . "<br>";
    echo "- العملاء النشطين: " . ($stats['active_customers'] ?? 0) . "<br>";
    echo "- عملاء جدد هذا الشهر: " . ($stats['new_this_month'] ?? 0) . "<br>";
    
    // اختبار جلب العملاء
    $customers = $customerManager->getAllWithStats();
    echo "- عدد العملاء المحملين: " . count($customers) . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في كلاس العملاء: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار كلاس الطلبيات
echo "<h2>3. اختبار كلاس الطلبيات</h2>";
try {
    $orderManager = new Order();
    
    // اختبار جلب الإحصائيات
    $stats = $orderManager->getStats();
    echo "✅ إحصائيات الطلبيات:<br>";
    echo "- إجمالي الطلبيات: " . ($stats['total_orders'] ?? 0) . "<br>";
    echo "- طلبيات معلقة: " . ($stats['pending_orders'] ?? 0) . "<br>";
    echo "- طلبيات قيد التنفيذ: " . ($stats['processing_orders'] ?? 0) . "<br>";
    echo "- طلبيات مكتملة: " . ($stats['completed_orders'] ?? 0) . "<br>";
    echo "- إجمالي المبيعات: " . ($stats['total_sales'] ?? 0) . " جنيه<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في كلاس الطلبيات: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار الإعدادات
echo "<h2>4. اختبار نظام الإعدادات</h2>";
try {
    echo "✅ الإعدادات الأساسية:<br>";
    echo "- اسم النظام: " . SYSTEM_NAME . "<br>";
    echo "- إصدار النظام: " . SYSTEM_VERSION . "<br>";
    echo "- وضع التطوير: " . (DEBUG_MODE ? 'مفعل' : 'معطل') . "<br>";
    echo "- المنطقة الزمنية: " . DEFAULT_TIMEZONE . "<br>";
    echo "- اللغة الافتراضية: " . DEFAULT_LANGUAGE . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الإعدادات: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار المجلدات
echo "<h2>5. اختبار المجلدات المطلوبة</h2>";
$requiredDirs = [
    'assets' => ASSETS_PATH,
    'assets/css' => ASSETS_PATH . '/css',
    'assets/js' => ASSETS_PATH . '/js',
    'assets/images' => ASSETS_PATH . '/images',
    'classes' => CLASSES_PATH,
    'includes' => INCLUDES_PATH,
    'logs' => LOGS_PATH,
    'uploads' => UPLOADS_PATH
];

foreach ($requiredDirs as $name => $path) {
    if (is_dir($path)) {
        echo "✅ مجلد {$name}: موجود<br>";
    } else {
        echo "❌ مجلد {$name}: غير موجود<br>";
    }
}

echo "<hr>";

// اختبار الملفات الأساسية
echo "<h2>6. اختبار الملفات الأساسية</h2>";
$requiredFiles = [
    'CSS الرئيسي' => 'assets/css/main.css',
    'JavaScript الرئيسي' => 'assets/js/main.js',
    'كلاس قاعدة البيانات' => 'classes/Database.php',
    'كلاس العملاء' => 'classes/Customer.php',
    'كلاس الطلبيات' => 'classes/Order.php',
    'Header مشترك' => 'includes/header.php',
    'Footer مشترك' => 'includes/footer.php',
    'إعدادات النظام' => 'config/config.php',
    'Autoload' => 'config/autoload.php'
];

foreach ($requiredFiles as $name => $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✅ {$name}: موجود (" . Helper::formatFileSize($size) . ")<br>";
    } else {
        echo "❌ {$name}: غير موجود<br>";
    }
}

echo "<hr>";

// اختبار الوظائف المساعدة
echo "<h2>7. اختبار الوظائف المساعدة</h2>";
try {
    // اختبار تنظيف النص
    $testText = "<script>alert('test')</script>نص تجريبي";
    $cleanText = Helper::sanitize($testText);
    echo "✅ تنظيف النص: " . $cleanText . "<br>";
    
    // اختبار التحقق من البريد الإلكتروني
    $email = "<EMAIL>";
    $isValid = Helper::validateEmail($email);
    echo "✅ التحقق من البريد الإلكتروني: " . ($isValid ? 'صحيح' : 'خطأ') . "<br>";
    
    // اختبار تنسيق التاريخ
    $date = Helper::formatDate(date('Y-m-d H:i:s'));
    echo "✅ تنسيق التاريخ: " . $date . "<br>";
    
    // اختبار تنسيق المبلغ
    $money = Helper::formatMoney(1234.56);
    echo "✅ تنسيق المبلغ: " . $money . "<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الوظائف المساعدة: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار API
echo "<h2>8. اختبار API</h2>";
if (file_exists('api/quick_stats.php')) {
    echo "✅ API الإحصائيات السريعة: موجود<br>";
    echo "🔗 <a href='api/quick_stats.php' target='_blank'>اختبار API</a><br>";
} else {
    echo "❌ API الإحصائيات السريعة: غير موجود<br>";
}

echo "<hr>";

// ملخص النتائج
echo "<h2>9. ملخص النتائج</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border: 1px solid #4caf50;'>";
echo "<h3 style='color: #2e7d32; margin-top: 0;'>✅ تم اختبار النظام بنجاح!</h3>";
echo "<p><strong>النظام جاهز للاستخدام.</strong></p>";
echo "<ul>";
echo "<li>جميع الكلاسات تعمل بشكل صحيح</li>";
echo "<li>قاعدة البيانات متصلة ومتاحة</li>";
echo "<li>الملفات والمجلدات موجودة</li>";
echo "<li>الوظائف المساعدة تعمل</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";

// روابط سريعة
echo "<h2>10. روابط سريعة للاختبار</h2>";
echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
echo "<h4>اختبر الصفحات:</h4>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='add_customer.php'>إضافة عميل</a></li>";
echo "<li><a href='add_order.php'>إضافة طلبية</a></li>";
echo "<li><a href='view_orders.php'>عرض الطلبيات</a></li>";
echo "<li><a href='manage_customers.php'>إدارة العملاء</a></li>";
echo "<li><a href='system_check.php'>فحص النظام</a></li>";
echo "</ul>";
echo "</div>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h1, h2, h3 { color: #333; }
hr { margin: 20px 0; }
ul { margin: 10px 0; }
a { color: #1976d2; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
