/**
 * ملف JavaScript الرئيسي لنظام WIDDX OMS
 * يحتوي على جميع الوظائف التفاعلية للنظام
 */

// كلاس رئيسي لإدارة النظام
class WIDDXSystem {
    constructor() {
        this.loadedCustomers = new Set();
        this.init();
    }

    // تهيئة النظام
    init() {
        this.bindEvents();
        this.initAnimations();
        this.initTooltips();
    }

    // ربط الأحداث
    bindEvents() {
        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', () => {
            this.animateCards();
        });

        // تأكيد الحذف
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', (e) => {
                const message = element.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    }

    // تحميل طلبيات العميل
    loadCustomerOrders(customerId) {
        // تجنب التحميل المتكرر
        if (this.loadedCustomers.has(customerId)) {
            return;
        }

        const container = document.getElementById(`orders-container-${customerId}`);
        if (!container) return;

        // إظهار مؤشر التحميل
        container.innerHTML = `
            <div class="text-center py-3">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">جاري التحميل...</p>
            </div>
        `;

        // تحميل البيانات
        fetch(`get_customer_orders.php?customer_id=${customerId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(data => {
                container.innerHTML = data;
                this.loadedCustomers.add(customerId);
                this.animateOrderRows();
            })
            .catch(error => {
                console.error('Error loading customer orders:', error);
                container.innerHTML = `
                    <div class="alert alert-danger alert-custom">
                        <i class="fas fa-exclamation-triangle"></i> 
                        حدث خطأ في تحميل الطلبيات. يرجى المحاولة مرة أخرى.
                    </div>
                `;
            });
    }

    // تحديث حالة الطلبية
    updateOrderStatus(orderId, status) {
        // التحقق من صحة البيانات
        if (!orderId || !status) {
            this.showAlert('بيانات غير صحيحة', 'error');
            return;
        }

        // إظهار مؤشر التحميل
        this.showLoadingButton(event.target);

        const formData = new FormData();
        formData.append('order_id', orderId);
        formData.append('status', status);
        formData.append('update_status', '1');

        fetch('update_order_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert(data.message, 'success');
                // إعادة تحميل الصفحة بعد ثانيتين
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                this.showAlert(data.message || 'حدث خطأ في تحديث الحالة', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating order status:', error);
            this.showAlert('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            this.hideLoadingButton(event.target);
        });
    }

    // عرض رسالة تنبيه
    showAlert(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const icon = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-triangle',
            'warning': 'fas fa-exclamation-circle',
            'info': 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        const alertHtml = `
            <div class="alert ${alertClass} alert-custom alert-dismissible fade show" role="alert">
                <i class="${icon}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إدراج الرسالة في أعلى الصفحة
        const container = document.querySelector('.container');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // إزالة الرسالة تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }

    // إظهار مؤشر التحميل على الزر
    showLoadingButton(button) {
        if (!button) return;
        
        button.disabled = true;
        button.innerHTML = '<span class="loading-spinner"></span> جاري التحديث...';
    }

    // إخفاء مؤشر التحميل من الزر
    hideLoadingButton(button) {
        if (!button) return;
        
        button.disabled = false;
        // استعادة النص الأصلي (يمكن تحسينه)
        button.innerHTML = button.getAttribute('data-original-text') || 'تحديث';
    }

    // تحريك البطاقات عند التحميل
    animateCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                card.classList.add('fade-in');
            }, index * 100);
        });
    }

    // تحريك صفوف الطلبيات
    animateOrderRows() {
        const rows = document.querySelectorAll('.order-row');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(20px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }

    // تهيئة الرسوم المتحركة
    initAnimations() {
        // تأثير hover للبطاقات
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    // تهيئة التلميحات
    initTooltips() {
        // تهيئة Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // تأكيد الحذف المتقدم
    confirmDelete(element, itemName = 'العنصر') {
        const result = confirm(`هل أنت متأكد من حذف ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);
        return result;
    }

    // تحديث الإحصائيات في الوقت الفعلي
    updateStats() {
        fetch('api/get_stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الإحصائيات في الواجهة
                    Object.keys(data.stats).forEach(key => {
                        const element = document.getElementById(`stat-${key}`);
                        if (element) {
                            element.textContent = data.stats[key];
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error updating stats:', error);
            });
    }

    // البحث المباشر
    liveSearch(input, targetContainer) {
        const searchTerm = input.value.toLowerCase();
        const items = document.querySelectorAll(`${targetContainer} .searchable-item`);
        
        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = 'block';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });
    }

    // تصدير البيانات
    exportData(format = 'excel', type = 'orders') {
        const url = `export.php?format=${format}&type=${type}`;
        window.open(url, '_blank');
    }

    // طباعة التقرير
    printReport(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير WIDDX OMS</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="assets/css/main.css" rel="stylesheet">
                    <style>
                        body { direction: rtl; }
                        .no-print { display: none !important; }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// إنشاء مثيل من النظام
const widdxSystem = new WIDDXSystem();

// وظائف عامة للاستخدام في الصفحات
function loadCustomerOrders(customerId) {
    widdxSystem.loadCustomerOrders(customerId);
}

function updateOrderStatus(orderId, status) {
    widdxSystem.updateOrderStatus(orderId, status);
}

function confirmDelete(element, itemName) {
    return widdxSystem.confirmDelete(element, itemName);
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(() => {
    widdxSystem.updateStats();
}, 30000);
