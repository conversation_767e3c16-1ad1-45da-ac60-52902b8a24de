<?php
/**
 * كلاس قاعدة البيانات المحسن لنظام WIDDX OMS
 * يتبع نمط Singleton ويوفر وظائف متقدمة لإدارة قاعدة البيانات
 */

class Database {
    private static $instance = null;
    private $host;
    private $user;
    private $pass;
    private $dbname;
    private $dbh;
    private $stmt;
    private $error;
    private $lastQuery;
    
    // إعدادات قاعدة البيانات
    private function __construct() {
        $this->host = DB_HOST ?? 'localhost';
        $this->user = DB_USER ?? 'root';
        $this->pass = DB_PASS ?? '';
        $this->dbname = DB_NAME ?? 'widdx_oms';
        
        $this->connect();
    }
    
    // منع الاستنساخ
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
    
    // الحصول على مثيل واحد من الكلاس
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    // الاتصال بقاعدة البيانات
    private function connect() {
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';
        
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
            PDO::ATTR_EMULATE_PREPARES => false
        ];

        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            $this->logError("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل الاتصال بقاعدة البيانات");
        }
    }
    
    // تحضير الاستعلام
    public function query($query) {
        $this->lastQuery = $query;
        try {
            $this->stmt = $this->dbh->prepare($query);
            return $this;
        } catch(PDOException $e) {
            $this->logError("Query preparation failed: " . $e->getMessage() . " | Query: " . $query);
            throw new Exception("خطأ في تحضير الاستعلام");
        }
    }
    
    // ربط القيم مع التحقق من النوع
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        
        try {
            $this->stmt->bindValue($param, $value, $type);
            return $this;
        } catch(PDOException $e) {
            $this->logError("Parameter binding failed: " . $e->getMessage());
            throw new Exception("خطأ في ربط المعاملات");
        }
    }
    
    // تنفيذ الاستعلام
    public function execute() {
        try {
            return $this->stmt->execute();
        } catch(PDOException $e) {
            $this->logError("Query execution failed: " . $e->getMessage() . " | Query: " . $this->lastQuery);
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    // الحصول على جميع النتائج
    public function resultset() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // الحصول على نتيجة واحدة
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // عدد الصفوف المتأثرة
    public function rowCount() {
        return $this->stmt->rowCount();
    }
    
    // آخر ID مدرج
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }
    
    // بدء المعاملة
    public function beginTransaction() {
        try {
            return $this->dbh->beginTransaction();
        } catch(PDOException $e) {
            $this->logError("Transaction start failed: " . $e->getMessage());
            throw new Exception("خطأ في بدء المعاملة");
        }
    }
    
    // تأكيد المعاملة
    public function commit() {
        try {
            return $this->dbh->commit();
        } catch(PDOException $e) {
            $this->logError("Transaction commit failed: " . $e->getMessage());
            throw new Exception("خطأ في تأكيد المعاملة");
        }
    }
    
    // إلغاء المعاملة
    public function rollBack() {
        try {
            return $this->dbh->rollBack();
        } catch(PDOException $e) {
            $this->logError("Transaction rollback failed: " . $e->getMessage());
            throw new Exception("خطأ في إلغاء المعاملة");
        }
    }
    
    // تنفيذ استعلام مباشر (للاستعلامات البسيطة)
    public function exec($query) {
        try {
            return $this->dbh->exec($query);
        } catch(PDOException $e) {
            $this->logError("Direct query execution failed: " . $e->getMessage() . " | Query: " . $query);
            throw new Exception("خطأ في تنفيذ الاستعلام المباشر");
        }
    }
    
    // التحقق من وجود جدول
    public function tableExists($tableName) {
        $query = "SHOW TABLES LIKE :tableName";
        $this->query($query);
        $this->bind(':tableName', $tableName);
        $result = $this->single();
        return !empty($result);
    }
    
    // التحقق من وجود عمود في جدول
    public function columnExists($tableName, $columnName) {
        $query = "SHOW COLUMNS FROM `{$tableName}` LIKE :columnName";
        $this->query($query);
        $this->bind(':columnName', $columnName);
        $result = $this->single();
        return !empty($result);
    }
    
    // الحصول على معلومات قاعدة البيانات
    public function getDatabaseInfo() {
        $info = [];
        
        // معلومات الاتصال
        $info['host'] = $this->host;
        $info['database'] = $this->dbname;
        $info['charset'] = 'utf8mb4';
        
        // إصدار MySQL
        $result = $this->dbh->query("SELECT VERSION() as version")->fetch();
        $info['mysql_version'] = $result['version'];
        
        // حجم قاعدة البيانات
        $query = "SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                  FROM information_schema.tables 
                  WHERE table_schema = :dbname";
        $this->query($query);
        $this->bind(':dbname', $this->dbname);
        $result = $this->single();
        $info['size_mb'] = $result['size_mb'] ?? 0;
        
        return $info;
    }
    
    // تسجيل الأخطاء
    private function logError($message) {
        $logFile = __DIR__ . '/../logs/database_errors.log';
        $logDir = dirname($logFile);
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    // تنظيف الموارد
    public function close() {
        $this->stmt = null;
        $this->dbh = null;
    }
    
    // الحصول على آخر خطأ
    public function getError() {
        return $this->error;
    }
    
    // الحصول على آخر استعلام
    public function getLastQuery() {
        return $this->lastQuery;
    }
    
    // التحقق من حالة الاتصال
    public function isConnected() {
        try {
            $this->dbh->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    // إعادة الاتصال في حالة انقطاعه
    public function reconnect() {
        $this->close();
        $this->connect();
    }
    
    // تنفيذ استعلام مع معاملات (طريقة مختصرة)
    public function run($query, $params = []) {
        $this->query($query);
        
        foreach ($params as $param => $value) {
            $this->bind($param, $value);
        }
        
        return $this->execute();
    }
    
    // الحصول على نتائج مع معاملات (طريقة مختصرة)
    public function select($query, $params = []) {
        $this->query($query);
        
        foreach ($params as $param => $value) {
            $this->bind($param, $value);
        }
        
        return $this->resultset();
    }
    
    // الحصول على نتيجة واحدة مع معاملات (طريقة مختصرة)
    public function selectOne($query, $params = []) {
        $this->query($query);
        
        foreach ($params as $param => $value) {
            $this->bind($param, $value);
        }
        
        return $this->single();
    }
}
