<?php
/**
 * API للإحصائيات السريعة
 * يوفر إحصائيات سريعة للعرض في شريط التنقل
 */

header('Content-Type: application/json; charset=utf-8');

// تحميل النظام
require_once '../config/autoload.php';

try {
    // إنشاء مثيلات من الكلاسات
    $customerManager = new Customer();
    $orderManager = new Order();
    $db = Database::getInstance();
    
    // إحصائيات اليوم
    $today = date('Y-m-d');
    
    // طلبيات اليوم
    $todayOrdersQuery = "SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = :today";
    $db->query($todayOrdersQuery);
    $db->bind(':today', $today);
    $todayOrders = $db->single()['count'] ?? 0;
    
    // عملاء جدد اليوم
    $todayCustomersQuery = "SELECT COUNT(*) as count FROM customers WHERE DATE(created_at) = :today";
    $db->query($todayCustomersQuery);
    $db->bind(':today', $today);
    $todayCustomers = $db->single()['count'] ?? 0;
    
    // مبيعات اليوم
    $todaySalesQuery = "SELECT SUM(total_amount) as total FROM orders 
                       WHERE DATE(created_at) = :today AND status = 'completed'";
    $db->query($todaySalesQuery);
    $db->bind(':today', $today);
    $todaySales = $db->single()['total'] ?? 0;
    
    // الطلبيات المعلقة (للإشعارات)
    $pendingOrdersQuery = "SELECT COUNT(*) as count FROM orders WHERE status = 'pending'";
    $db->query($pendingOrdersQuery);
    $pendingOrders = $db->single()['count'] ?? 0;
    
    // الطلبيات المتأخرة (أكثر من 3 أيام)
    $overdueOrdersQuery = "SELECT COUNT(*) as count FROM orders 
                          WHERE status IN ('pending', 'processing') 
                          AND created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)";
    $db->query($overdueOrdersQuery);
    $overdueOrders = $db->single()['count'] ?? 0;
    
    // إنشاء الإشعارات
    $notifications = [];
    
    if ($pendingOrders > 0) {
        $notifications[] = [
            'type' => 'warning',
            'message' => "لديك {$pendingOrders} طلبية معلقة تحتاج للمراجعة",
            'url' => 'view_orders.php?status=pending',
            'icon' => 'fas fa-clock'
        ];
    }
    
    if ($overdueOrders > 0) {
        $notifications[] = [
            'type' => 'danger',
            'message' => "لديك {$overdueOrders} طلبية متأخرة",
            'url' => 'view_orders.php?overdue=1',
            'icon' => 'fas fa-exclamation-triangle'
        ];
    }
    
    if ($todayOrders > 0) {
        $notifications[] = [
            'type' => 'success',
            'message' => "تم إضافة {$todayOrders} طلبية جديدة اليوم",
            'url' => 'view_orders.php?date=' . $today,
            'icon' => 'fas fa-plus-circle'
        ];
    }
    
    // إعداد الاستجابة
    $response = [
        'success' => true,
        'stats' => [
            'today_orders' => $todayOrders,
            'today_customers' => $todayCustomers,
            'today_sales' => number_format($todaySales, 0),
            'pending_orders' => $pendingOrders,
            'overdue_orders' => $overdueOrders
        ],
        'notifications' => $notifications,
        'timestamp' => time()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ
    $response = [
        'success' => false,
        'message' => 'حدث خطأ في جلب الإحصائيات',
        'error' => DEBUG_MODE ? $e->getMessage() : null
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
    // تسجيل الخطأ
    Helper::logError("Quick stats API error: " . $e->getMessage());
}
?>
